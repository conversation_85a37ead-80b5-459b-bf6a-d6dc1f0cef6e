<script lang="ts">
	import type { PageProps } from './$types';
	import * as Card from '$lib/components/ui/card';
	import * as Tabs from '$lib/components/ui/tabs';
	import { Badge } from '$lib/components/ui/badge';
	import WbsMappingInterface from '$lib/components/tender/WbsMappingInterface.svelte';
	import BudgetTransferInterface from '$lib/components/tender/BudgetTransferInterface.svelte';
	import { enhance } from '$app/forms';
	import { toast } from 'svelte-sonner';
	import { formatCurrency } from '$lib/utils';

	const { data }: PageProps = $props();

	const lineItem = $derived(data.lineItem);
	const wbsItems = $derived(data.wbsItems || []);
	const budgetTransfers = $derived(data.budgetTransfers || []);
	const existingMappings = $derived(data.lineItem.tender_wbs_mapping || []);

	// Calculate mapping coverage statistics
	const mappingStats = $derived.by(() => {
		const totalMappings = existingMappings.length;
		const totalCoverage = existingMappings.reduce(
			(sum, mapping) => sum + (mapping.coverage_percentage || 0),
			0,
		);
		const averageCoverage = totalMappings > 0 ? totalCoverage / totalMappings : 0;

		return {
			totalMappings,
			totalCoverage,
			averageCoverage,
		};
	});

	// Get currency info from tender (fallback to default)
	const currencyInfo = $derived.by(() => ({
		symbol: 'kr',
		symbolPosition: 'after' as 'before' | 'after',
	}));

	function handleMappingCreate(payload: {
		lineItemId: string;
		mapping: {
			wbs_library_item_id: string;
			coverage_percentage?: number;
			coverage_quantity?: number;
			notes?: string;
		};
	}) {
		// This will be handled by the form action
		console.log('Creating mapping:', payload);
	}

	function handleMappingUpdate(payload: {
		mappingId: string;
		mapping: {
			coverage_percentage?: number;
			coverage_quantity?: number;
			notes?: string;
		};
	}) {
		// This will be handled by the form action
		console.log('Updating mapping:', payload);
	}

	function handleMappingDelete(payload: { mappingId: string }) {
		// This will be handled by the form action
		console.log('Deleting mapping:', payload);
	}

	function handleTransferCreate(payload: {
		lineItemId?: string;
		transfer: {
			from_wbs_library_item_id: string;
			to_wbs_library_item_id: string;
			transfer_amount: number;
			reason: string;
		};
	}) {
		// This will be handled by the form action
		console.log('Creating transfer:', payload);
	}

	function handleTransferValidate(payload: {
		from_wbs_library_item_id: string;
		to_wbs_library_item_id: string;
		transfer_amount: number;
	}) {
		// This will be handled by the form action
		console.log('Validating transfer:', payload);
	}
</script>

<svelte:head>
	<title>WBS Mapping - {lineItem?.description} - {data.project?.name}</title>
</svelte:head>

<div class="space-y-6 p-4 lg:p-6">
	<h1 class="sr-only">WBS Mapping</h1>

	<!-- Line Item Summary -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="flex items-center justify-between">
				<span class="text-2xl">Line Item Details</span>
				<Badge variant="outline">#{lineItem?.line_number}</Badge>
			</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-1 gap-4 md:grid-cols-3">
				<div>
					<div class="text-sm font-medium text-gray-500">Description</div>
					<div class="text-sm">{lineItem?.description || 'No description'}</div>
				</div>
				<div>
					<div class="text-sm font-medium text-gray-500">Quantity & Unit</div>
					<div class="text-sm">
						{#if lineItem?.quantity && lineItem?.unit}
							{lineItem.quantity} {lineItem.unit}
						{:else}
							-
						{/if}
					</div>
				</div>
				<div>
					<div class="text-sm font-medium text-gray-500">Subtotal</div>
					<div class="text-sm font-semibold">
						{#if lineItem?.subtotal}
							{formatCurrency(lineItem.subtotal, {
								symbol: currencyInfo.symbol,
								symbolPosition: currencyInfo.symbolPosition,
								fallback: '-',
							})}
						{:else}
							-
						{/if}
					</div>
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Mapping Statistics -->
	<Card.Root>
		<Card.Header>
			<Card.Title class="text-base">Mapping Overview</Card.Title>
		</Card.Header>
		<Card.Content>
			<div class="grid grid-cols-3 gap-4">
				<div class="text-center">
					<div class="text-2xl font-bold">{mappingStats.totalMappings}</div>
					<div class="text-sm text-gray-500">WBS Mappings</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold">{mappingStats.totalCoverage.toFixed(1)}%</div>
					<div class="text-sm text-gray-500">Total Coverage</div>
				</div>
				<div class="text-center">
					<div class="text-2xl font-bold">{mappingStats.averageCoverage.toFixed(1)}%</div>
					<div class="text-sm text-gray-500">Avg Coverage</div>
				</div>
			</div>
		</Card.Content>
	</Card.Root>

	<!-- Main Interface -->
	<Tabs.Root value="mapping" class="w-full">
		<Tabs.List class="grid w-full grid-cols-2">
			<Tabs.Trigger value="mapping">WBS Mapping</Tabs.Trigger>
			<Tabs.Trigger value="transfers">Budget Transfers</Tabs.Trigger>
		</Tabs.List>

		<Tabs.Content value="mapping" class="space-y-4">
			<WbsMappingInterface
				lineItemId={lineItem?.tender_line_item_id || ''}
				lineItemDescription={lineItem?.description || ''}
				availableWbsItems={wbsItems}
				{existingMappings}
				currencySymbol={currencyInfo.symbol}
				symbolPosition={currencyInfo.symbolPosition}
				onMappingCreate={handleMappingCreate}
				onMappingUpdate={handleMappingUpdate}
				onMappingDelete={handleMappingDelete}
			/>
		</Tabs.Content>

		<Tabs.Content value="transfers" class="space-y-4">
			<BudgetTransferInterface
				availableWbsItems={wbsItems}
				existingTransfers={budgetTransfers}
				lineItemId={lineItem?.tender_line_item_id}
				currencySymbol={currencyInfo.symbol}
				symbolPosition={currencyInfo.symbolPosition}
				onTransferCreate={handleTransferCreate}
				onTransferValidate={handleTransferValidate}
			/>
		</Tabs.Content>
	</Tabs.Root>
</div>

<!-- Hidden forms for server actions -->
<div class="hidden">
	<!-- Create Mapping Form -->
	<form
		method="POST"
		action="?/createMapping"
		use:enhance={() => {
			return ({ result, update }) => {
				if (result.type === 'success') {
					toast.success('WBS mapping created successfully');
				} else if (result.type === 'failure') {
					toast.error('Failed to create WBS mapping');
				}
				update();
			};
		}}
	>
		<!-- Form fields will be populated by JavaScript -->
	</form>

	<!-- Update Mapping Form -->
	<form
		method="POST"
		action="?/updateMapping"
		use:enhance={() => {
			return ({ result, update }) => {
				if (result.type === 'success') {
					toast.success('WBS mapping updated successfully');
				} else if (result.type === 'failure') {
					toast.error('Failed to update WBS mapping');
				}
				update();
			};
		}}
	>
		<!-- Form fields will be populated by JavaScript -->
	</form>

	<!-- Delete Mapping Form -->
	<form
		method="POST"
		action="?/deleteMapping"
		use:enhance={() => {
			return ({ result, update }) => {
				if (result.type === 'success') {
					toast.success('WBS mapping deleted successfully');
				} else if (result.type === 'failure') {
					toast.error('Failed to delete WBS mapping');
				}
				update();
			};
		}}
	>
		<!-- Form fields will be populated by JavaScript -->
	</form>

	<!-- Create Transfer Form -->
	<form
		method="POST"
		action="?/createTransfer"
		use:enhance={() => {
			return ({ result, update }) => {
				if (result.type === 'success') {
					toast.success('Budget transfer created successfully');
				} else if (result.type === 'failure') {
					toast.error('Failed to create budget transfer');
				}
				update();
			};
		}}
	>
		<!-- Form fields will be populated by JavaScript -->
	</form>

	<!-- Validate Transfer Form -->
	<form method="POST" action="?/validateTransfer" use:enhance>
		<!-- Form fields will be populated by JavaScript -->
	</form>
</div>
