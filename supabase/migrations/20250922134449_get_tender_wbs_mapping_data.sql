set
	check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.get_tender_wbs_mapping_data (
	project_id_param uuid,
	tender_line_item_id_param uuid
) RETURNS TABLE (
	project jsonb,
	line_item jsonb,
	wbs_items jsonb,
	budget_transfers jsonb
) LANGUAGE plpgsql SECURITY DEFINER
SET
	search_path TO '' AS $function$
DECLARE
	project_json jsonb;
	line_item_json jsonb;
	wbs_items_json jsonb := '[]'::jsonb;
	budget_transfers_json jsonb := '[]'::jsonb;
	v_active_budget_version_id uuid;
BEGIN
	-- Verify project access and load minimal project data
	SELECT
		row_to_json(project_row)::jsonb,
		project_row.active_budget_version_id
	INTO
		project_json,
		v_active_budget_version_id
	FROM (
		SELECT
			project_id,
			name,
			active_budget_version_id
		FROM public.project
		WHERE project_id = project_id_param
	) AS project_row;

	IF project_json IS NULL THEN
		RAISE EXCEPTION 'Project not found';
	END IF;

	IF NOT public.current_user_has_entity_access('project', project_id_param) THEN
		RAISE EXCEPTION 'Access denied to project';
	END IF;

	-- Load tender line item with existing mappings
	SELECT
		to_jsonb(line_item_data)
	INTO line_item_json
	FROM (
		SELECT
			tli.*,
			COALESCE(
				(
					SELECT jsonb_agg(
						jsonb_build_object(
							'tender_wbs_mapping_id', twm.tender_wbs_mapping_id,
							'tender_line_item_id', twm.tender_line_item_id,
							'wbs_library_item_id', twm.wbs_library_item_id,
							'coverage_percentage', twm.coverage_percentage,
							'coverage_quantity', twm.coverage_quantity,
							'notes', twm.notes,
							'created_at', twm.created_at,
							'updated_at', twm.updated_at,
							'wbs_library_item', jsonb_build_object(
								'code', wli.code,
								'description', wli.description,
								'level', wli.level
							)
						)
						ORDER BY twm.created_at
					)
					FROM public.tender_wbs_mapping twm
					JOIN public.wbs_library_item wli ON wli.wbs_library_item_id = twm.wbs_library_item_id
					WHERE twm.tender_line_item_id = tli.tender_line_item_id
				),
				'[]'::jsonb
			) AS tender_wbs_mapping
		FROM public.tender_line_item tli
		JOIN public.tender_revision tr ON tli.tender_revision_id = tr.tender_revision_id
		JOIN public.tender t ON tr.tender_id = t.tender_id
		WHERE tli.tender_line_item_id = tender_line_item_id_param
			AND t.project_id = project_id_param
		LIMIT 1
	) AS line_item_data;

	IF line_item_json IS NULL THEN
		RAISE EXCEPTION 'Tender line item not found';
	END IF;

	-- Load WBS items with budget information, deduplicated by library item
	SELECT
		COALESCE(jsonb_agg(wbs_item ORDER BY (wbs_item ->> 'code')), '[]'::jsonb)
	INTO wbs_items_json
	FROM (
		SELECT DISTINCT ON (wli.wbs_library_item_id)
			jsonb_build_object(
				'wbs_library_item_id', wli.wbs_library_item_id,
				'wbs_library_id', wli.wbs_library_id,
				'level', wli.level,
				'in_level_code', wli.in_level_code,
				'parent_item_id', wli.parent_item_id,
				'code', wli.code,
				'description', wli.description,
				'cost_scope', wli.cost_scope,
				'item_type', wli.item_type,
				'client_id', wli.client_id,
				'project_id', wli.project_id,
				'created_at', wli.created_at,
				'updated_at', wli.updated_at,
				'budget_amount', COALESCE(bvi.quantity, 0) * COALESCE(bvi.unit_rate, 0) * COALESCE(bvi.factor, 1),
				'budget_quantity', bvi.quantity,
				'budget_unit_rate', bvi.unit_rate,
				'unit', bvi.unit
			) AS wbs_item
	FROM public.wbs_library_item wli
	LEFT JOIN public.budget_version_item bvi ON bvi.wbs_library_item_id = wli.wbs_library_item_id
	LEFT JOIN public.budget_version bv ON bvi.budget_version_id = bv.budget_version_id
	WHERE (wli.project_id = project_id_param OR wli.project_id IS NULL)
		AND (
			v_active_budget_version_id IS NULL
			OR bvi.budget_version_id = v_active_budget_version_id
			OR bvi.budget_version_id IS NULL
		)
	ORDER BY
		wli.wbs_library_item_id,
		CASE
			WHEN v_active_budget_version_id IS NOT NULL AND bvi.budget_version_id = v_active_budget_version_id THEN 0
			ELSE 1
		END,
		bv.created_at DESC,
		bvi.created_at DESC
	) AS wbs_items;

	-- Load budget transfers for the project
	SELECT
		COALESCE(
			jsonb_agg(
				jsonb_build_object(
					'budget_transfer_id', bt.budget_transfer_id,
					'project_id', bt.project_id,
					'tender_line_item_id', bt.tender_line_item_id,
					'from_wbs_library_item_id', bt.from_wbs_library_item_id,
					'to_wbs_library_item_id', bt.to_wbs_library_item_id,
					'transfer_amount', bt.transfer_amount,
					'reason', bt.reason,
					'created_at', bt.created_at,
					'updated_at', bt.updated_at,
					'from_wbs_item', jsonb_build_object(
						'code', from_wli.code,
						'description', from_wli.description
					),
					'to_wbs_item', jsonb_build_object(
						'code', to_wli.code,
						'description', to_wli.description
					)
				)
				ORDER BY bt.created_at DESC
			),
			'[]'::jsonb
		)
	INTO budget_transfers_json
	FROM public.budget_transfer bt
	LEFT JOIN public.wbs_library_item from_wli ON from_wli.wbs_library_item_id = bt.from_wbs_library_item_id
	LEFT JOIN public.wbs_library_item to_wli ON to_wli.wbs_library_item_id = bt.to_wbs_library_item_id
	WHERE bt.project_id = project_id_param;

	RETURN QUERY
	SELECT
		project_json,
		line_item_json,
		wbs_items_json,
		budget_transfers_json;
END;
$function$;
